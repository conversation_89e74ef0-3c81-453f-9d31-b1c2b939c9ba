/* Support Page Styles - Benedicto College Library Management System */

.support-container {
  background: white;
}

.support-card {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.support-header {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.support-divider {
  background: linear-gradient(90deg, #0ea5e9 0%, #3b82f6 25%, #f59e0b 75%, #f97316 100%);
  height: 4px;
  border-radius: 2px;
}

.help-category {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.help-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.contact-card {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 5px solid #3b82f6;
}

.faq-item {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-left: 3px solid #f59e0b;
}

.support-icon {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
}

.category-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

.urgent-box {
  background: white;
  border: 2px solid #dc2626;
  border-radius: 8px;
}

.success-tip {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-left: 5px solid #22c55e;
}

/* FAQ Styles - Professional ug clean design */
.faq-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.faq-item:hover {
  border-left-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.faq-answer {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}

/* Search and Filter Styles */
.filter-btn {
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.filter-btn.active {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Search Input Enhancement */
#faqSearch:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Suggestion Cards */
.suggestion-card {
  transition: all 0.3s ease;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Popular Search Tags */
.popular-search-tag {
  transition: all 0.2s ease;
}

.popular-search-tag:hover {
  transform: scale(1.05);
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .support-card {
    margin: 0.5rem;
    padding: 1.5rem;
  }

  .help-category {
    margin-bottom: 1rem;
  }

  .support-header {
    font-size: 2rem;
  }

  .filter-btn {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .faq-item h3 {
    font-size: 1rem;
  }

  .suggestion-card {
    padding: 1rem;
  }
}

@media (max-width: 640px) {
  .filter-btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }
}
