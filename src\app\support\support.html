<div class="min-h-screen support-container relative overflow-hidden">
  <!-- Background pattern para sa support page, friendly ug welcoming design -->
  <div class="absolute inset-0 opacity-6">
    <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
      <defs>
        <pattern id="support-grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <circle cx="10" cy="10" r="1" fill="#3b82f6" opacity="0.3"/>
          <circle cx="5" cy="5" r="0.5" fill="#f59e0b" opacity="0.4"/>
          <circle cx="15" cy="15" r="0.5" fill="#0ea5e9" opacity="0.4"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#support-grid)" />
    </svg>
  </div>

  <!-- Enhanced professional header with breadcrumb and quick access -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
      <!-- Main header row -->
      <div class="flex justify-between items-center">
        <!-- Logo section -->
        <div class="flex-1 flex justify-start items-center">
          <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
            <img
              src="assets/images/BcLogo.png"
              alt="Benedicto College Logo"
              class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
              onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
              onload="console.log('Logo loaded successfully:', this.src);"
            >
          </a>
        </div>

        <!-- Breadcrumb navigation (hidden on mobile) -->
        <div class="hidden lg:flex flex-1 justify-center">
          <nav class="flex items-center space-x-2 text-sm">
            <a routerLink="/" class="text-gray-300 hover:text-orange-400 transition duration-300">Home</a>
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-orange-400 font-medium">Help & Support</span>
          </nav>
        </div>

        <!-- Navigation and quick access -->
        <div class="flex items-center space-x-2 lg:space-x-4">
          <!-- Quick access links with tooltips - always visible -->
          <div class="flex items-center space-x-4 md:space-x-6">
            <!-- Main Website -->
            <div class="relative group">
              <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                Main Website
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- Help & Support -->
            <div class="relative group">
              <a routerLink="/support" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                Help & Support
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- About Us -->
            <div class="relative group">
              <a routerLink="/about" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                About Us
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- Contact Us -->
            <div class="relative group">
              <a routerLink="/contact" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                Contact Us
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>

            <!-- My Account -->
            <div class="relative group">
              <a routerLink="/login" class="text-gray-300 hover:text-orange-400 transition duration-300 p-2 rounded-lg hover:bg-gray-800 flex items-center">
                <svg class="w-6 h-6 text-black" fill="none" stroke="black" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </a>
              <!-- Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-50">
                My Account
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button class="text-white focus:outline-none p-2 relative z-50">
              <svg class="w-6 h-6 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main content section para sa support -->
  <main class="relative z-10 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      
      <!-- Support Header -->
      <div class="text-center mb-12">
        <div >
          <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
          </svg>
        </div>
        <h1 class="text-4xl sm:text-5xl font-bold support-header mb-4">Student Support Center</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          We're here to help you make the most of your library experience at Benedicto College. Find answers, get assistance, and access resources.
        </p>
        <div class="support-divider w-32 mx-auto mt-6"></div>
      </div>

      <!-- Emergency Contact -->
      <div class="max-w-4xl mx-auto">
        <div class="urgent-box p-6 mb-8">
          <div class="flex items-center mb-4">
            <svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <h3 class="text-xl font-bold text-gray-800">Priority Support Contact</h3>
          </div>
          <p class="text-gray-700 mb-4">For urgent technical issues or account-related concerns, please contact our support team through the following channels:</p>
          <div class="flex flex-col sm:flex-row gap-4">
            <a href="tel:+***********" class="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 text-center">
              📞 Phone: (*************
            </a>
            <a href="mailto:support&#64;benedictocollege.edu.ph" class="bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 text-center">
              ✉️ Email: support&#64;benedictocollege.edu.ph
            </a>
          </div>
        </div>
           <hr class="border-gray-300 my-20 w-100" >

      </div>



      <!-- Search and Filter Section -->
      <div class="mb-12">
        <div class="bg-gray-50 py-8 px-6 rounded-lg border-l-4 border-blue-500">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Find Help Quickly</h2>

          <!-- Search Bar -->
          <div class="max-w-2xl mx-auto mb-6">
            <div class="relative">
              <input
                type="text"
                id="faqSearch"
                placeholder="Search FAQs, guides, or enter your question..."
                class="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                oninput="searchFAQs()"
              >
              <svg class="absolute left-4 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>

          <!-- Quick Filter Buttons -->
          <div class="flex flex-wrap justify-center gap-3 mb-6">
            <button onclick="filterFAQs('all')" class="filter-btn active px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300">All Topics</button>
            <button onclick="filterFAQs('account')" class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition duration-300">Account & Login</button>
            <button onclick="filterFAQs('books')" class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition duration-300">Books & Resources</button>
            <button onclick="filterFAQs('technical')" class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition duration-300">Technical Issues</button>
            <button onclick="filterFAQs('borrowing')" class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition duration-300">Borrowing & Returns</button>
            <button onclick="filterFAQs('facilities')" class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition duration-300">Facilities</button>
          </div>

          <!-- Popular Searches -->
          <div class="text-center">
            <p class="text-gray-600 mb-3">Popular searches:</p>
            <div class="flex flex-wrap justify-center gap-2">
              <span onclick="document.getElementById('faqSearch').value='password reset'; searchFAQs();" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300">Password Reset</span>
              <span onclick="document.getElementById('faqSearch').value='book renewal'; searchFAQs();" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300">Book Renewal</span>
              <span onclick="document.getElementById('faqSearch').value='study room'; searchFAQs();" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300">Study Room</span>
              <span onclick="document.getElementById('faqSearch').value='library hours'; searchFAQs();" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300">Library Hours</span>
              <span onclick="document.getElementById('faqSearch').value='overdue fines'; searchFAQs();" class="cursor-pointer bg-white px-3 py-1 rounded-full text-sm text-gray-600 hover:bg-blue-100 hover:text-blue-700 transition duration-300">Overdue Fines</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Comprehensive FAQ Section -->
      <div class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>

        <!-- No Results Message -->
        <div id="noResults" class="hidden text-center py-8">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.816-6.207-2.175C5.25 12.09 5.25 11.438 5.25 10.5V6.75C5.25 4.679 6.929 3 9 3h6c2.071 0 3.75 1.679 3.75 3.75v3.75c0 .938 0 1.59-.543 2.325A7.962 7.962 0 0112 15z"></path>
          </svg>
          <h3 class="text-xl font-semibold text-gray-700 mb-2">No results found</h3>
          <p class="text-gray-500">Try different keywords or browse our categories below</p>
        </div>

        <!-- FAQ Items -->
        <div class="space-y-4" id="faqContainer"></div>
      </div>

      <!-- Quick Help Suggestions -->
      <div class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Quick Help & Suggestions</h2>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <!-- Self-Service Options -->
          <div class="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
            <div class="flex items-center mb-4">
              <svg class="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              <h3 class="text-xl font-semibold text-gray-900">Self-Service</h3>
            </div>
            <ul class="text-gray-700 space-y-2">
              <li>• Reset password online</li>
              <li>• Renew books from "My Account"</li>
              <li>• Reserve study rooms</li>
              <li>• Check due dates and fines</li>
              <li>• Download digital resources</li>
            </ul>
          </div>

          <!-- Before Contacting Support -->
          <div class="bg-orange-50 p-6 rounded-lg border-l-4 border-orange-500">
            <div class="flex items-center mb-4">
              <svg class="w-8 h-8 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 class="text-xl font-semibold text-gray-900">Try First</h3>
            </div>
            <ul class="text-gray-700 space-y-2">
              <li>• Clear browser cache</li>
              <li>• Check internet connection</li>
              <li>• Verify Student ID format</li>
              <li>• Try different browser</li>
              <li>• Check spam folder for emails</li>
            </ul>
          </div>

          <!-- Emergency Procedures -->
          <div class="bg-red-50 p-6 rounded-lg border-l-4 border-red-500">
            <div class="flex items-center mb-4">
              <svg class="w-8 h-8 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
              <h3 class="text-xl font-semibold text-gray-900">Urgent Issues</h3>
            </div>
            <ul class="text-gray-700 space-y-2">
              <li>• Call (*************</li>
              <li>• Visit library desk immediately</li>
              <li>• Email: support&#64;benedictocollege.edu.ph</li>
              <li>• Include Student ID in all requests</li>
              <li>• Describe issue clearly</li>
            </ul>
          </div>
        </div>

        <!-- Smart Suggestions Based on Common Issues -->
        <div class="bg-gray-50 p-6 rounded-lg">
          <h3 class="text-xl font-semibold text-gray-900 mb-4 text-center">Smart Suggestions</h3>
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-semibold text-gray-800 mb-3">If you're having login issues:</h4>
              <ol class="text-gray-700 space-y-1 list-decimal list-inside">
                <li>Verify your Student ID format (2000-00000)</li>
                <li>Check if Caps Lock is enabled</li>
                <li>Try the "Forgot Password" option</li>
                <li>Clear browser cookies and cache</li>
                <li>Contact support with your Student ID ready</li>
              </ol>
            </div>
            <div>
              <h4 class="font-semibold text-gray-800 mb-3">If you can't find a book:</h4>
              <ol class="text-gray-700 space-y-1 list-decimal list-inside">
                <li>Try different search terms (author, title, subject)</li>
                <li>Check if it's available in digital format</li>
                <li>Look for similar books in the same category</li>
                <li>Submit an acquisition request</li>
                <li>Ask a librarian for research assistance</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      <!-- Live Chat and Support Options -->
      <div class="mb-12">
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-8 rounded-lg text-center">
          <h3 class="text-2xl font-bold mb-4">Still Need Help?</h3>
          <p class="text-blue-100 mb-6">Our support team is ready to assist you with any library-related questions or issues.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="tel:+***********" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              Call Support
            </a>
            <a href="mailto:support&#64;benedictocollege.edu.ph" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              Email Support
            </a>
            <button onclick="window.location.href='/contact'" class="bg-orange-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-600 transition duration-300 flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Visit Us
            </button>
          </div>
        </div>
      </div>

      <!-- Contact Information -->
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>

      <!-- Primary Support -->
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Primary Support</h3>
      <p class="text-gray-700 mb-2"><span class="font-medium">Email:</span> support&#64;benedictocollege.edu.ph</p>
      <p class="text-gray-700 mb-2"><span class="font-medium">Phone:</span> (*************</p>
      <p class="text-gray-700 mb-8"><span class="font-medium">Hours:</span> Monday-Friday: 8:00 AM - 5:00 PM</p>

      <!-- Walk-in Support -->
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Walk-in Support</h3>
      <p class="text-gray-700 mb-2"><span class="font-medium">Location:</span> Library Main Floor, Information Desk</p>
      <p class="text-gray-700 mb-2"><span class="font-medium">Hours:</span> Monday-Saturday: 7:00 AM - 8:00 PM</p>
      <p class="text-gray-700 mb-8"><span class="font-medium">Staff:</span> Librarians & IT Support Specialists</p>

      <!-- Quick Tips -->
      <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Tips for Faster Support</h3>
      <p class="text-gray-700 mb-2">• Have your Student ID ready when contacting support</p>
      <p class="text-gray-700 mb-2">• Describe the specific error message or issue you're experiencing</p>
      <p class="text-gray-700 mb-2">• Try clearing your browser cache before reporting technical issues</p>
      <p class="text-gray-700 mb-2">• Check your internet connection for access problems</p>
      <p class="text-gray-700 mb-8">• Include screenshots when reporting visual issues via email</p>

    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500 relative z-10 mt-12">
    <div class="container mx-auto px-4 sm:px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <div class="flex justify-center md:justify-start space-x-4">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"></path>
                </svg>
              </a>
              <a href="tel:+***********" class="bg-gray-800 hover:bg-green-500 p-3 rounded-full transition duration-300 transform hover:scale-110">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px and beyond) -->
      <div class="hidden xl:block">
        <div class="flex justify-between items-start mb-8">
          <!-- Get in Touch Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span>(*************</span>
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>Benedicto College Library</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/about" class="block text-gray-400 hover:text-green-400 transition duration-300">About Us</a>
              <a routerLink="/contact" class="block text-gray-400 hover:text-green-400 transition duration-300">Contact</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="flex-1">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <a href="https://facebook.com/benedictocollege" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Facebook</a>
            <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-400 hover:text-green-400 transition duration-300 mr-6">Website</a>
            <a href="tel:+***********" class="text-gray-400 hover:text-green-400 transition duration-300">Phone</a>
          </div>
        </div>
      </div>



      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button
    id="scrollToTopBtn"
    onclick="scrollToTop()"
    class="fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 z-50 opacity-0 invisible"
    title="Scroll to top"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
    </svg>
  </button>
</div>

<script>
  // Comprehensive FAQ Data para sa Library Management System
  const faqData = [
    // Account & Login FAQs
    {
      category: 'account',
      question: 'How do I reset my password?',
      answer: 'Click "Forgot Password" on the login page, enter your Student ID, and check your email for reset instructions. If you don\'t receive an email within 10 minutes, contact support at (*************.',
      keywords: ['password', 'reset', 'forgot', 'login', 'email']
    },
    {
      category: 'account',
      question: 'What is the correct Student ID format?',
      answer: 'Your Student ID follows the format: 2000-00000 (year-number). For example: 2024-12345. Use this exact format when logging in.',
      keywords: ['student id', 'format', 'login', 'number', 'year']
    },
    {
      category: 'account',
      question: 'Why can\'t I log into my account?',
      answer: 'Common issues: 1) Incorrect Student ID format, 2) Caps Lock is on, 3) Account not activated, 4) Browser cache issues. Try clearing your browser cache or contact support.',
      keywords: ['login', 'account', 'access', 'error', 'problem']
    },
    {
      category: 'account',
      question: 'How do I activate my new account?',
      answer: 'New students receive activation instructions via email within 24 hours of enrollment. Check your spam folder or contact the library desk for manual activation.',
      keywords: ['activate', 'new', 'account', 'student', 'enrollment']
    },
    {
      category: 'account',
      question: 'Can I change my account information?',
      answer: 'You can update your email and phone number in "My Profile" after logging in. For name or Student ID changes, visit the Registrar\'s Office first.',
      keywords: ['change', 'update', 'profile', 'information', 'email']
    },

    // Books & Resources FAQs
    {
      category: 'books',
      question: 'How do I search for books in the library?',
      answer: 'Use the search bar on the homepage. You can search by title, author, ISBN, or subject. Use filters to narrow results by availability, format, or location.',
      keywords: ['search', 'books', 'find', 'title', 'author', 'isbn']
    },
    {
      category: 'books',
      question: 'How do I reserve a book that\'s currently borrowed?',
      answer: 'Click "Reserve" on the book\'s detail page. You\'ll be notified via email when it becomes available. Reservations are held for 3 days.',
      keywords: ['reserve', 'book', 'borrowed', 'hold', 'notification']
    },
    {
      category: 'books',
      question: 'How many books can I borrow at once?',
      answer: 'Undergraduate students: 5 books, Graduate students: 10 books, Faculty: 15 books. Borrowing limits reset when books are returned.',
      keywords: ['borrow', 'limit', 'how many', 'books', 'student']
    },
    {
      category: 'books',
      question: 'How long can I keep borrowed books?',
      answer: 'Regular books: 14 days, Reference books: 3 days, Thesis/Research materials: 7 days. Check your "My Account" for specific due dates.',
      keywords: ['borrow', 'period', 'due date', 'how long', 'return']
    },
    {
      category: 'books',
      question: 'Can I renew my borrowed books?',
      answer: 'Yes, books can be renewed once for the same period if no one has reserved them. Renew online through "My Account" or visit the library desk.',
      keywords: ['renew', 'renewal', 'extend', 'books', 'online']
    },
    {
      category: 'books',
      question: 'How do I access digital resources and databases?',
      answer: 'Log into your account and click "Digital Resources". Use your library credentials to access academic databases, e-books, and online journals.',
      keywords: ['digital', 'database', 'online', 'ebooks', 'journals']
    },
    {
      category: 'books',
      question: 'What if a book I need is not available in the library?',
      answer: 'Submit an "Acquisition Request" through your account. We review requests monthly and prioritize based on academic relevance and demand.',
      keywords: ['not available', 'request', 'acquisition', 'purchase', 'new book']
    },

    // Technical Issues FAQs
    {
      category: 'technical',
      question: 'The website is loading slowly. What should I do?',
      answer: 'Try: 1) Clear browser cache and cookies, 2) Disable browser extensions, 3) Try a different browser, 4) Check your internet connection. Contact IT support if issues persist.',
      keywords: ['slow', 'loading', 'website', 'performance', 'browser']
    },
    {
      category: 'technical',
      question: 'Which browsers are supported?',
      answer: 'Supported browsers: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+. For best experience, keep your browser updated and enable JavaScript.',
      keywords: ['browser', 'supported', 'compatibility', 'chrome', 'firefox']
    },
    {
      category: 'technical',
      question: 'I\'m getting error messages. What do they mean?',
      answer: 'Common errors: "Session Expired" - log in again, "Access Denied" - check permissions, "Server Error" - try again later. Screenshot errors for support.',
      keywords: ['error', 'message', 'session', 'server', 'access denied']
    },
    {
      category: 'technical',
      question: 'Can I use the library system on my mobile phone?',
      answer: 'Yes! Our system is mobile-responsive. Use any modern mobile browser. For best experience, add our website to your home screen.',
      keywords: ['mobile', 'phone', 'responsive', 'app', 'smartphone']
    },
    {
      category: 'technical',
      question: 'Why can\'t I download or print documents?',
      answer: 'Check: 1) Pop-up blocker settings, 2) PDF reader installation, 3) Printer connectivity, 4) Document permissions. Some resources have download restrictions.',
      keywords: ['download', 'print', 'pdf', 'documents', 'popup']
    },

    // Borrowing & Returns FAQs
    {
      category: 'borrowing',
      question: 'What are the library\'s borrowing policies?',
      answer: 'Valid Student ID required, maximum borrowing limits apply, overdue fines are ₱5/day per book, damaged books must be replaced or paid for.',
      keywords: ['policy', 'borrowing', 'rules', 'fines', 'damaged']
    },
    {
      category: 'borrowing',
      question: 'How much are overdue fines?',
      answer: 'Overdue fines: ₱5 per day per book. Maximum fine: ₱200 per book. Pay fines at the library desk or through the online payment system.',
      keywords: ['overdue', 'fines', 'late', 'payment', 'cost']
    },
    {
      category: 'borrowing',
      question: 'Where do I return borrowed books?',
      answer: 'Return books at: 1) Library circulation desk during operating hours, 2) Book drop box (available 24/7) near the main entrance, 3) Any library branch.',
      keywords: ['return', 'where', 'circulation', 'book drop', 'location']
    },
    {
      category: 'borrowing',
      question: 'What happens if I lose a borrowed book?',
      answer: 'Report lost books immediately. You\'ll need to: 1) Pay replacement cost, 2) Pay processing fee (₱100), 3) Submit a lost book form at the circulation desk.',
      keywords: ['lost', 'book', 'replacement', 'cost', 'fee']
    },
    {
      category: 'borrowing',
      question: 'Can I borrow books for someone else?',
      answer: 'No, books can only be borrowed using your own Student ID. Each person must use their own account for borrowing and returning materials.',
      keywords: ['borrow', 'someone else', 'proxy', 'other person', 'id']
    },

    // Facilities FAQs
    {
      category: 'facilities',
      question: 'What are the library operating hours?',
      answer: 'Monday-Friday: 7:00 AM - 8:00 PM, Saturday: 8:00 AM - 5:00 PM, Sunday: 1:00 PM - 6:00 PM. Extended hours during finals week.',
      keywords: ['hours', 'operating', 'open', 'schedule', 'time']
    },
    {
      category: 'facilities',
      question: 'How do I reserve a study room?',
      answer: 'Book study rooms online through "Facilities" → "Room Reservation". Rooms can be reserved up to 7 days in advance for 2-hour slots.',
      keywords: ['study room', 'reserve', 'booking', 'facilities', 'group']
    },
    {
      category: 'facilities',
      question: 'Are there computers available for student use?',
      answer: 'Yes! 50 computers available on the 2nd floor. Log in with your Student ID. Time limit: 2 hours per session, can be extended if no queue.',
      keywords: ['computers', 'lab', 'student', 'available', 'time limit']
    },
    {
      category: 'facilities',
      question: 'Is there WiFi in the library?',
      answer: 'Free WiFi available throughout the library. Network: "BC-Library-WiFi". Use your Student ID and password to connect.',
      keywords: ['wifi', 'internet', 'wireless', 'connection', 'network']
    },
    {
      category: 'facilities',
      question: 'Can I bring food and drinks into the library?',
      answer: 'Water in covered containers is allowed. Food and other beverages are restricted to designated areas on the ground floor only.',
      keywords: ['food', 'drinks', 'water', 'eating', 'beverages']
    },
    {
      category: 'facilities',
      question: 'Are there printing and photocopying services?',
      answer: 'Yes! Printing: ₱2/page (B&W), ₱5/page (Color). Photocopying: ₱1/page. Pay using your student card or cash at the service desk.',
      keywords: ['printing', 'photocopy', 'services', 'cost', 'price']
    },
    {
      category: 'facilities',
      question: 'Is there a quiet study area?',
      answer: 'Yes! Silent study zones on the 3rd floor. No talking, phone calls, or group discussions allowed. Perfect for individual study and research.',
      keywords: ['quiet', 'silent', 'study', 'area', 'individual']
    }
  ];

  let currentFilter = 'all';

  // Initialize FAQ display on page load
  document.addEventListener('DOMContentLoaded', function() {
    displayFAQs(faqData);
  });

  // Search functionality
  function searchFAQs() {
    const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
    let filteredFAQs = faqData;

    // Apply category filter first
    if (currentFilter !== 'all') {
      filteredFAQs = filteredFAQs.filter(faq => faq.category === currentFilter);
    }

    // Apply search filter
    if (searchTerm) {
      filteredFAQs = filteredFAQs.filter(faq =>
        faq.question.toLowerCase().includes(searchTerm) ||
        faq.answer.toLowerCase().includes(searchTerm) ||
        faq.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm))
      );
    }

    displayFAQs(filteredFAQs);
  }

  // Filter by category
  function filterFAQs(category) {
    currentFilter = category;

    // Update button styles
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.remove('bg-blue-600', 'text-white', 'active');
      btn.classList.add('bg-gray-200', 'text-gray-700');
    });

    event.target.classList.remove('bg-gray-200', 'text-gray-700');
    event.target.classList.add('bg-blue-600', 'text-white', 'active');

    // Clear search and apply filter
    document.getElementById('faqSearch').value = '';
    searchFAQs();
  }

  // Display FAQs
  function displayFAQs(faqs) {
    const container = document.getElementById('faqContainer');
    const noResults = document.getElementById('noResults');

    if (faqs.length === 0) {
      container.innerHTML = '';
      noResults.classList.remove('hidden');
      return;
    }

    noResults.classList.add('hidden');

    container.innerHTML = faqs.map((faq, index) => `
      <div class="faq-item bg-white border border-gray-200 rounded-lg overflow-hidden" data-category="${faq.category}">
        <button
          class="w-full px-6 py-4 text-left focus:outline-none focus:bg-gray-50 hover:bg-gray-50 transition duration-300"
          onclick="toggleFAQ(${index})"
        >
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900 pr-4">${faq.question}</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-300" id="icon-${index}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="faq-answer hidden px-6 pb-4" id="answer-${index}">
          <div class="border-t border-gray-100 pt-4">
            <p class="text-gray-700 leading-relaxed">${faq.answer}</p>
            <div class="mt-3 flex flex-wrap gap-2">
              ${faq.keywords.slice(0, 3).map(keyword =>
                `<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">${keyword}</span>`
              ).join('')}
            </div>
          </div>
        </div>
      </div>
    `).join('');
  }

  // Toggle FAQ answer visibility
  function toggleFAQ(index) {
    const answer = document.getElementById(`answer-${index}`);
    const icon = document.getElementById(`icon-${index}`);

    if (answer.classList.contains('hidden')) {
      answer.classList.remove('hidden');
      icon.style.transform = 'rotate(180deg)';
    } else {
      answer.classList.add('hidden');
      icon.style.transform = 'rotate(0deg)';
    }
  }

  // Scroll to Top functionality
  function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  // Show/hide scroll to top button based on scroll position
  window.addEventListener('scroll', function() {
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');
    if (window.pageYOffset > 300) {
      scrollToTopBtn.classList.remove('opacity-0', 'invisible');
      scrollToTopBtn.classList.add('opacity-100', 'visible');
    } else {
      scrollToTopBtn.classList.remove('opacity-100', 'visible');
      scrollToTopBtn.classList.add('opacity-0', 'invisible');
    }
  });
</script>
